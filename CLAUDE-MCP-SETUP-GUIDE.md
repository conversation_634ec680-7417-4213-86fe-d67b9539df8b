# Claude MCP Integration Setup Guide

## 🎯 Overview

This guide walks you through setting up <PERSON>'s Model Context Protocol (MCP) integration with Augment for the Figma-Storybook sync system. This integration enables intelligent design analysis, code generation, and conflict resolution.

## 📋 Prerequisites

- Node.js 18+ installed
- VS Code with Augment extension
- Claude API key from Anthropic
- Figma access token
- Running Storybook instance

## 🚀 Quick Setup

### 1. Configure Environment Variables

Create a `.env` file in the project root:

```bash
# Copy the example file
cp .env.example .env

# Edit with your actual API keys
nano .env
```

Required variables:
```bash
CLAUDE_API_KEY=your-claude-api-key-here
FIGMA_ACCESS_TOKEN=your-figma-token
STORYBOOK_URL=http://localhost:6006
```

### 2. Install Dependencies

```bash
cd mcp-server
npm install
```

### 3. Test the Setup

```bash
# Test connections
npm run test-connection

# Test Claude integration (requires API key)
npm run test
```

### 4. Start the MCP Server

```bash
# Using the startup script
./start-mcp-server.sh

# Or manually
npm start
```

### 5. Verify Augment Configuration

The VS Code settings have been updated to include three MCP servers:

1. **Figma MCP** - Access Figma designs
2. **Storybook MCP** - Access Storybook components  
3. **Claude MCP** - Intelligent analysis and code generation

Restart VS Code to apply the new settings.

## 🛠️ Available Tools

### Design Analysis
```
@claude analyze this Figma button design and compare it with our Storybook Button component
```

### Code Generation
```
@claude generate a React component from these Figma design tokens
```

### Conflict Resolution
```
@claude help resolve these design system conflicts between Figma and Storybook
```

### Component Matching
```
@claude find the best matching Storybook component for this Figma design
```

## 🧪 Testing the Integration

### Test 1: Basic Connection
```bash
cd mcp-server
npm run test-connection
```

Expected output:
- ✅ Dependencies imported successfully
- ✅ File system permissions OK
- ⚠️ API keys status (set if configured)

### Test 2: Claude Integration
```bash
npm run test
```

This will test:
- Design analysis capabilities
- Code generation
- Conflict resolution
- Component matching

### Test 3: Augment Integration

In VS Code:
1. Open Command Palette (`Cmd+Shift+P`)
2. Type "Augment: Analyze Figma Design Change"
3. Or use chat: `@claude analyze button component`

## 🔧 Configuration Details

### MCP Server Configuration

The server provides these tools:

1. **analyze_design_change**
   - Analyzes Figma design changes
   - Identifies potential breaking changes
   - Suggests migration paths

2. **generate_component_update**
   - Generates React/Vue/Angular components
   - Creates Storybook stories
   - Includes TypeScript types

3. **resolve_conflicts**
   - Analyzes design system conflicts
   - Provides resolution strategies
   - Prioritizes fixes by impact

4. **find_storybook_component**
   - Matches Figma designs to Storybook components
   - Suggests modifications needed
   - Provides confidence scores

### VS Code Settings

The configuration includes:
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "figma-storybook-sync-claude",
        "command": "node",
        "args": ["./mcp-server/index.js"],
        "env": {
          "CLAUDE_API_KEY": "your-key",
          "FIGMA_ACCESS_TOKEN": "your-token"
        }
      }
    ]
  }
}
```

## 🎨 Usage Examples

### Example 1: Analyze Design Changes

```
I have updated the Button component in Figma. Can you analyze the changes and tell me what needs to be updated in our Storybook?

Figma node ID: 919:93785
Current Storybook component: Button
```

### Example 2: Generate New Component

```
Please generate a Card component from these Figma design tokens:
- Background: #FFFFFF
- Border radius: 8px
- Padding: 16px
- Shadow: 0px 2px 8px rgba(0,0,0,0.1)
```

### Example 3: Resolve Conflicts

```
We have conflicts between our Figma designs and Storybook components:
1. Button color: Figma #6643B5 vs Storybook #5a3ba0
2. Spacing: Figma 9px 17px vs Storybook 8px 16px

Please help resolve these conflicts.
```

## 🚨 Troubleshooting

### Common Issues

1. **"Claude API key not provided"**
   - Check your `.env` file
   - Ensure `CLAUDE_API_KEY` is set correctly
   - Restart the MCP server

2. **"MCP server not responding"**
   - Check if the server is running: `npm start`
   - Verify VS Code settings are correct
   - Restart VS Code

3. **"Storybook not accessible"**
   - Start Storybook: `npm run storybook`
   - Check the URL in `STORYBOOK_URL`
   - Verify port 6006 is available

4. **"Dependencies import failed"**
   - Run `npm install` in mcp-server directory
   - Check Node.js version (18+ required)

### Debug Mode

Enable debug logging:
```bash
export DEBUG=true
npm start
```

### Log Files

Check logs in:
- MCP server console output
- VS Code Developer Tools (Help > Toggle Developer Tools)
- Augment extension logs

## 🔄 Workflow Integration

### Automated Sync Process

1. **Design Change Detection**
   - Figma webhook triggers analysis
   - Claude analyzes design changes
   - Conflicts are identified

2. **Code Generation**
   - Updated components generated
   - Storybook stories created
   - Tests generated

3. **Review Process**
   - Pull request created
   - Visual diffs generated
   - Team review requested

4. **Deployment**
   - Approved changes merged
   - Design system updated
   - Documentation updated

## 📚 Next Steps

1. **Set up Figma webhooks** for automatic change detection
2. **Configure GitHub Actions** for automated PR creation
3. **Implement visual regression testing**
4. **Create approval dashboard** for design changes
5. **Set up monitoring** for sync status

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Run the connection tests: `npm run test-connection`
3. Check the logs for error messages
4. Verify all API keys are correctly set

The Claude MCP integration is now ready to help you maintain consistency between your Figma designs and Storybook components!
