import { ClaudeClient } from './claude-client.js';
import { config } from 'dotenv';

// Load environment variables
config();

async function testClaudeIntegration() {
  console.log('🧪 Testing Claude Integration...\n');
  
  const client = new ClaudeClient(process.env.CLAUDE_API_KEY);
  
  // Test 1: Design Analysis
  console.log('📊 Test 1: Design Analysis');
  const testParams = {
    componentName: 'Button',
    figmaData: {
      type: 'RECTANGLE',
      fills: [{
        type: 'SOLID',
        color: { r: 0.4, g: 0.26, b: 0.71, a: 1 } // #6643B5
      }],
      cornerRadius: 6,
      absoluteBoundingBox: { width: 120, height: 40 },
      style: {
        fontSize: 14,
        fontWeight: 500,
        fontFamily: 'Inter'
      }
    },
    currentCode: `
      export const Button = styled.button\`
        background-color: #5a3ba0;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 400;
      \`;
    `
  };
  
  try {
    const analysis = await client.analyzeDesignChange(testParams);
    console.log('✅ Analysis Result:', JSON.stringify(analysis, null, 2));
  } catch (error) {
    console.log('❌ Analysis Error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Code Generation
  console.log('🔧 Test 2: Code Generation');
  const codeParams = {
    tokens: {
      colors: {
        primary: { hex: '#6643B5', rgba: 'rgba(102, 67, 181, 1)' }
      },
      typography: {
        fontSize: 14,
        fontWeight: 500,
        fontFamily: 'Inter'
      },
      spacing: {
        padding: { top: 9, right: 17, bottom: 9, left: 17 }
      },
      borders: {
        borderRadius: 6
      }
    },
    componentType: 'button',
    framework: 'react'
  };
  
  try {
    const code = await client.generateCode(codeParams);
    console.log('✅ Generated Code:', JSON.stringify(code, null, 2));
  } catch (error) {
    console.log('❌ Code Generation Error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 3: Conflict Resolution
  console.log('⚡ Test 3: Conflict Resolution');
  const conflictParams = {
    conflicts: [
      {
        type: 'color',
        description: 'Primary button color mismatch',
        figmaValue: '#6643B5',
        storybookValue: '#5a3ba0',
        severity: 'medium',
        components: ['Button', 'PrimaryButton']
      },
      {
        type: 'spacing',
        description: 'Button padding inconsistency',
        figmaValue: '9px 17px',
        storybookValue: '8px 16px',
        severity: 'low',
        components: ['Button']
      }
    ],
    context: {
      designSystem: 'School Attendance',
      framework: 'react',
      storybookVersion: '8.0'
    }
  };
  
  try {
    const resolution = await client.resolveConflicts(conflictParams);
    console.log('✅ Conflict Resolution:', JSON.stringify(resolution, null, 2));
  } catch (error) {
    console.log('❌ Conflict Resolution Error:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 4: Design Matching
  console.log('🎯 Test 4: Design Matching');
  const matchParams = {
    description: 'A purple button with rounded corners and medium padding',
    tokens: {
      backgroundColor: '#6643B5',
      borderRadius: '6px',
      padding: '9px 17px'
    }
  };
  
  try {
    const match = await client.analyzeDesignMatch(matchParams);
    console.log('✅ Design Match:', JSON.stringify(match, null, 2));
  } catch (error) {
    console.log('❌ Design Match Error:', error.message);
  }
  
  console.log('\n🎉 Claude integration test completed!');
}

// Run the test
testClaudeIntegration().catch(console.error);
