#!/bin/bash

# Figma-Storybook MCP Server Startup Script

echo "🚀 Starting Figma-Storybook MCP Server with Claude Integration"
echo "============================================================"

# Check if .env file exists
if [ ! -f "../.env" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp "../.env.example" "../.env"
    echo "📝 Please edit .env file with your API keys before running again."
    exit 1
fi

# Load environment variables
source ../.env

# Check required environment variables
required_vars=("CLAUDE_API_KEY" "FIGMA_ACCESS_TOKEN")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '   %s\n' "${missing_vars[@]}"
    echo "Please set these in your .env file."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Run connection test first
echo "🔗 Running connection tests..."
npm run test-connection

if [ $? -ne 0 ]; then
    echo "❌ Connection tests failed. Please check your configuration."
    exit 1
fi

echo "✅ Connection tests passed!"
echo ""

# Start the MCP server
echo "🎯 Starting MCP Server..."
echo "Press Ctrl+C to stop the server"
echo ""

npm start
