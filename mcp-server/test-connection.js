import { config } from 'dotenv';

// Load environment variables
config();

async function testConnections() {
  console.log('🔗 Testing MCP Server Connections...\n');
  
  // Test 1: Environment Variables
  console.log('📋 Test 1: Environment Variables');
  const requiredEnvVars = [
    'CLAUDE_API_KEY',
    'FIGMA_ACCESS_TOKEN',
    'STORYBOOK_URL'
  ];
  
  const envStatus = {};
  requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    envStatus[varName] = value ? '✅ Set' : '❌ Missing';
    console.log(`  ${varName}: ${envStatus[varName]}`);
  });
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: MCP Server Dependencies
  console.log('📦 Test 2: Dependencies');
  try {
    const { McpServer } = await import('@modelcontextprotocol/sdk/server/mcp.js');
    console.log('  ✅ MCP SDK imported successfully');
    
    const { ClaudeClient } = await import('./claude-client.js');
    console.log('  ✅ Claude Client imported successfully');
    
    const { DesignAnalyzer } = await import('./handlers/design-analyzer.js');
    console.log('  ✅ Design Analyzer imported successfully');
    
    const { CodeGenerator } = await import('./handlers/code-generator.js');
    console.log('  ✅ Code Generator imported successfully');
    
    const { ConflictResolver } = await import('./handlers/conflict-resolver.js');
    console.log('  ✅ Conflict Resolver imported successfully');
    
  } catch (error) {
    console.log('  ❌ Dependency import failed:', error.message);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 3: Storybook Connection
  console.log('📚 Test 3: Storybook Connection');
  const storybookUrl = process.env.STORYBOOK_URL || 'http://localhost:6006';
  
  try {
    const response = await fetch(`${storybookUrl}/iframe.html`);
    if (response.ok) {
      console.log(`  ✅ Storybook accessible at ${storybookUrl}`);
    } else {
      console.log(`  ⚠️  Storybook responded with status: ${response.status}`);
    }
  } catch (error) {
    console.log(`  ❌ Storybook not accessible at ${storybookUrl}`);
    console.log(`     Make sure Storybook is running: npm run storybook`);
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 4: Claude API Connection
  console.log('🤖 Test 4: Claude API Connection');
  if (process.env.CLAUDE_API_KEY) {
    try {
      const { ClaudeClient } = await import('./claude-client.js');
      const client = new ClaudeClient(process.env.CLAUDE_API_KEY);
      
      // Simple test to verify API key works
      const testResult = await client.analyzeDesignMatch({
        description: 'test',
        tokens: {}
      });
      
      console.log('  ✅ Claude API connection successful');
    } catch (error) {
      console.log('  ❌ Claude API connection failed:', error.message);
      console.log('     Check your CLAUDE_API_KEY in .env file');
    }
  } else {
    console.log('  ⚠️  Claude API key not set - using mock responses');
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 5: File System Permissions
  console.log('📁 Test 5: File System Permissions');
  try {
    const fs = await import('fs/promises');
    const testFile = './test-write-permission.tmp';
    
    await fs.writeFile(testFile, 'test');
    await fs.unlink(testFile);
    console.log('  ✅ File system write permissions OK');
  } catch (error) {
    console.log('  ❌ File system write permissions failed:', error.message);
  }
  
  console.log('\n🎯 Connection test completed!');
  
  // Summary
  console.log('\n📊 Summary:');
  const hasClaudeKey = !!process.env.CLAUDE_API_KEY;
  const hasFigmaKey = !!process.env.FIGMA_ACCESS_TOKEN;
  
  if (hasClaudeKey && hasFigmaKey) {
    console.log('✅ Ready for full integration with Claude and Figma');
  } else if (hasClaudeKey) {
    console.log('⚠️  Claude ready, but Figma token missing');
  } else if (hasFigmaKey) {
    console.log('⚠️  Figma ready, but Claude API key missing');
  } else {
    console.log('❌ Missing both Claude and Figma credentials');
  }
  
  console.log('\nNext steps:');
  console.log('1. Set up your .env file with API keys');
  console.log('2. Start Storybook: npm run storybook');
  console.log('3. Test the MCP server: npm test');
  console.log('4. Update VS Code settings with the new MCP server');
}

// Run the test
testConnections().catch(console.error);
