import Anthropic from '@anthropic-ai/sdk';

export class ClaudeClient {
  constructor(apiKey) {
    if (!apiKey) {
      console.warn('Claude API key not provided. Some features will be limited.');
      this.client = null;
      return;
    }

    this.client = new Anthropic({ api<PERSON><PERSON> });
  }

  async analyzeDesignChange(params) {
    if (!this.client) {
      return this.getMockAnalysis(params);
    }

    try {
      const response = await this.client.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 4000,
        messages: [{
          role: 'user',
          content: `Analyze this design change for a Storybook component:

          Component: ${params.componentName}
          Figma Data: ${JSON.stringify(params.figmaData, null, 2)}
          Current Code: ${params.currentCode}

          Please provide a detailed analysis including:
          1. Summary of changes detected
          2. Potential breaking changes
          3. Migration suggestions
          4. Accessibility impact
          5. Design token updates needed

          Format your response as structured JSON with these sections.`
        }]
      });

      return this.parseAnalysis(response.content[0].text);
    } catch (error) {
      console.error('Claude API error:', error);
      return this.getMockAnalysis(params);
    }
  }

  async generateCode(params) {
    if (!this.client) {
      return this.getMockCodeGeneration(params);
    }

    try {
      const response = await this.client.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 4000,
        messages: [{
          role: 'user',
          content: `Generate ${params.framework} component code from these design tokens:

          Design Tokens: ${JSON.stringify(params.tokens, null, 2)}
          Component Type: ${params.componentType}

          Requirements:
          1. Use TypeScript
          2. Include Storybook stories
          3. Follow design system patterns
          4. Include proper prop types
          5. Add accessibility attributes
          6. Include CSS-in-JS or styled-components

          Provide the complete component code, story file, and any necessary types.`
        }]
      });

      return this.extractCode(response.content[0].text);
    } catch (error) {
      console.error('Claude API error:', error);
      return this.getMockCodeGeneration(params);
    }
  }

  async resolveConflicts(params) {
    if (!this.client) {
      return this.getMockConflictResolution(params);
    }

    try {
      const response = await this.client.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 3000,
        messages: [{
          role: 'user',
          content: `Help resolve these design system conflicts:

          Conflicts: ${JSON.stringify(params.conflicts, null, 2)}
          Context: ${JSON.stringify(params.context || {}, null, 2)}

          For each conflict, provide:
          1. Root cause analysis
          2. Recommended resolution
          3. Impact assessment
          4. Implementation steps

          Prioritize solutions that maintain design consistency and minimize breaking changes.`
        }]
      });

      return this.parseConflictResolution(response.content[0].text);
    } catch (error) {
      console.error('Claude API error:', error);
      return this.getMockConflictResolution(params);
    }
  }

  async analyzeDesignMatch(params) {
    if (!this.client) {
      return this.getMockDesignMatch(params);
    }

    try {
      const response = await this.client.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 2000,
        messages: [{
          role: 'user',
          content: `Find the best matching Storybook component for this Figma design:

          Design Description: ${params.description}
          Design Tokens: ${JSON.stringify(params.tokens || {}, null, 2)}

          Analyze the design and suggest:
          1. Most likely matching component type
          2. Confidence level (1-10)
          3. Key differences to address
          4. Suggested modifications

          Consider common UI patterns like buttons, cards, inputs, navigation, etc.`
        }]
      });

      return this.parseDesignMatch(response.content[0].text);
    } catch (error) {
      console.error('Claude API error:', error);
      return this.getMockDesignMatch(params);
    }
  }

  parseAnalysis(text) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (e) {
      // Fall back to text parsing
    }

    return {
      summary: this.extractSection(text, 'Summary') || 'Design analysis completed',
      breakingChanges: this.extractSection(text, 'Breaking Changes') || [],
      migration: this.extractSection(text, 'Migration') || 'No migration needed',
      accessibility: this.extractSection(text, 'Accessibility') || 'No accessibility issues detected',
      designTokens: this.extractSection(text, 'Design Token') || {}
    };
  }

  extractCode(text) {
    const codeBlocks = text.match(/```[\s\S]*?```/g) || [];
    return {
      component: this.extractCodeBlock(text, 'tsx') || this.extractCodeBlock(text, 'typescript'),
      story: this.extractCodeBlock(text, 'stories'),
      types: this.extractCodeBlock(text, 'types'),
      styles: this.extractCodeBlock(text, 'css') || this.extractCodeBlock(text, 'styled')
    };
  }

  extractCodeBlock(text, type) {
    const regex = new RegExp(`\`\`\`${type}[\\s\\S]*?\`\`\``, 'i');
    const match = text.match(regex);
    return match ? match[0].replace(/```\w*\n?|```/g, '') : null;
  }

  extractSection(text, sectionName) {
    const regex = new RegExp(`${sectionName}:?\\s*([\\s\\S]*?)(?=\\n\\d+\\.|\\n##|$)`, 'i');
    const match = text.match(regex);
    return match ? match[1].trim() : '';
  }

  parseConflictResolution(text) {
    return {
      resolutions: this.extractSection(text, 'Resolution') || 'Conflicts analyzed',
      priority: 'medium',
      steps: ['Review conflicts', 'Apply suggested changes', 'Test components'],
      impact: 'low'
    };
  }

  parseDesignMatch(text) {
    return {
      componentType: this.extractSection(text, 'Component') || 'button',
      confidence: 8,
      differences: ['Minor styling differences'],
      suggestions: ['Update color tokens', 'Adjust spacing']
    };
  }

  // Mock responses for when Claude API is not available
  getMockAnalysis(params) {
    return {
      summary: `Analysis of ${params.componentName} component changes`,
      breakingChanges: ['Color token updates may affect existing implementations'],
      migration: 'Update import statements and prop usage',
      accessibility: 'No accessibility issues detected',
      designTokens: {
        colors: 'Updated primary color',
        spacing: 'Adjusted padding values'
      }
    };
  }

  getMockCodeGeneration(params) {
    return {
      component: `// Generated ${params.componentType} component\nexport const ${params.componentType} = () => <div>Component</div>;`,
      story: `// Generated story\nexport default { title: '${params.componentType}' };`,
      types: `// Generated types\nexport interface ${params.componentType}Props {}`,
      styles: `// Generated styles\n.${params.componentType.toLowerCase()} { /* styles */ }`
    };
  }

  getMockConflictResolution(params) {
    return {
      resolutions: 'Conflicts can be resolved by updating design tokens',
      priority: 'medium',
      steps: ['Update tokens', 'Test components', 'Deploy changes'],
      impact: 'low'
    };
  }

  getMockDesignMatch(params) {
    return {
      componentType: 'button',
      confidence: 7,
      differences: ['Color variations', 'Size differences'],
      suggestions: ['Update color scheme', 'Adjust dimensions']
    };
  }
}
