{"github.copilot.enable": {"*": false}, "augment.chat.userGuidelines": "", "augment.advanced": {"mcpServers": [{"name": "github.com/GLips/Figma-Context-MCP", "command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}, {"name": "github.com/m-yoshiro/storybook-mcp", "command": "node", "args": ["/Users/<USER>/Desktop/Figma MCP/storybook-mcp/build/index.js", "/Users/<USER>/Desktop/Figma MCP/storybook-mcp/storybook-sample/storybook-static/index.json"]}, {"name": "figma-storybook-sync-claude", "command": "node", "args": ["/Users/<USER>/Desktop/Figma MCP/mcp-server/index.js"], "env": {"CLAUDE_API_KEY": "************************************************************************************************************", "FIGMA_ACCESS_TOKEN": "*********************************************", "STORYBOOK_URL": "http://localhost:6006"}}]}, "augment.mcp.enabled": true, "augment.mcp.defaultServer": "figma-storybook-sync-claude", "augment.customCommands": [{"name": "Analyze Figma Design Change", "command": "augment.mcp.execute", "args": {"tool": "analyze_design_change", "server": "figma-storybook-sync-claude"}}, {"name": "Generate Component Update", "command": "augment.mcp.execute", "args": {"tool": "generate_component_update", "server": "figma-storybook-sync-claude"}}, {"name": "Resolve Design Conflicts", "command": "augment.mcp.execute", "args": {"tool": "resolve_conflicts", "server": "figma-storybook-sync-claude"}}, {"name": "Find Matching Storybook Component", "command": "augment.mcp.execute", "args": {"tool": "find_storybook_component", "server": "figma-storybook-sync-claude"}}]}